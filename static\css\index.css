:root {
    --primary-color: #2563eb;
    --success-color: #16a34a;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --bg-light: #f9fafb;
    --border-color: #e5e7eb;
    --neutral-dark: #1f2937;
    --neutral-light: #f3f4f6;
}

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    font-family: "Segoe UI", Tahoma, sans-serif;
    font-size: 16px;
    color: var(--neutral-dark);
    min-height: 100vh;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.main-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    margin: 40px auto;
    max-width: 1200px;
}

.header {
    background: linear-gradient(90deg, var(--primary-color), #3b82f6);
    color: white;
    padding: 2rem 1rem;
    text-align: center;
    border-radius: 12px;
    margin-bottom: 2rem;
}

body.bg-light {
    background: linear-gradient(to bottom right, #f9fafc, #e5e7eb);
}

.card {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: none;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.preview-section,
.scanner-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    transition: all 0.3s ease;
}

.preview-section:hover,
.scanner-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.section-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
    color: white;
    padding: 0.8rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 25px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    margin-bottom: 1.5rem;
    display: inline-block;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.section-title:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(0,0,0,0.25);
}

#current-dataset,
#current-template {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Modal overlay styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
}

.close-btn:hover {
    color: #1f2937;
}

.paper-size-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.email-config-note {
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.5rem;
}

.email-config-control {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.8) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(233, 236, 239, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.email-config-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.email-config-control input {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.email-config-control input:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

#emailSection {
    background: linear-gradient(135deg, rgba(227, 242, 253, 0.9) 0%, rgba(243, 229, 245, 0.9) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(225, 190, 231, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    animation: emailSectionGlow 3s ease-in-out infinite alternate;
}

@keyframes emailSectionGlow {
    0% { box-shadow: 0 0 10px rgba(171, 71, 188, 0.2); }
    100% { box-shadow: 0 0 20px rgba(171, 71, 188, 0.4); }
}

#emailSection input {
    border: 1px solid #ce93d8;
    border-radius: 6px;
}

#emailSection input:focus {
    border-color: #ab47bc;
    box-shadow: 0 0 0 0.2rem rgba(171, 71, 188, 0.25);
}

#sendEmailBtn {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

#sendEmailBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

#sendEmailBtn:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

#noEmailSection {
    background: linear-gradient(135deg, rgba(255, 243, 205, 0.9) 0%, rgba(255, 235, 238, 0.9) 100%);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(255, 193, 7, 0.3);
    backdrop-filter: blur(10px);
}

#noEmailSection .alert {
    background: transparent;
    border: none;
    margin: 0;
    padding: 0;
    color: #856404;
}

#noEmailSection .alert i {
    color: #ffc107;
}

/* Email input enhancements */
#recipientEmail {
    transition: all 0.3s ease;
}

#recipientEmail:focus {
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(171, 71, 188, 0.4);
}

#recipientEmail.auto-populated {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(129, 199, 132, 0.1) 100%);
    border-color: #4caf50;
}

/* Enhanced alert styles */
.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    border: 1px solid rgba(13, 202, 240, 0.3);
    color: #055160;
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(32, 201, 151, 0.1) 100%);
    border: 1px solid rgba(25, 135, 84, 0.3);
    color: #0a3622;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 235, 59, 0.1) 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #664d03;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(244, 67, 54, 0.1) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #58151c;
}

#activatePanel {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    padding: 2.5rem;
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

#activatePanel:hover {
    transform: translateY(-3px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

#activatePanel strong {
    font-size: 1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(90deg, #782fff, #c084fc);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(120, 47, 255, 0.2);
    margin-bottom: 0.5rem;
}

#activatePanel span {
    font-family: monospace;
    font-size: 0.9rem;
    color: #1f2937;
    word-break: break-all;
}

#activatePanel .btn-outline-primary {
    padding: 0.5rem 1.2rem;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 25px;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box;
    color: #667eea;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#activatePanel .btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: left 0.3s ease;
    z-index: -1;
}

#activatePanel .btn-outline-primary:hover::before {
    left: 0;
}

#activatePanel .btn-outline-primary:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

#activatePanel .btn-success {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 200% 200%;
    animation: gradientShift 6s ease infinite;
    border: none;
    color: white;
    font-weight: 700;
    padding: 0.8rem 2rem;
    font-size: 1rem;
    border-radius: 50px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

#activatePanel .btn-success::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

#activatePanel .btn-success:hover::before {
    width: 300px;
    height: 300px;
}

#activatePanel .btn-success:hover {
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
    transform: translateY(-3px) scale(1.05);
}

#activatePanel .paper-size-control {
    margin-top: 2rem;
}

#activatePanel label {
    font-weight: 600;
    font-size: 0.95rem;
    color: #374151;
    margin-bottom: 0.25rem;
}

#activatePanel select {
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: #f9fafb;
    font-size: 0.9rem;
    width: 100%;
    max-width: 300px;
}

#customSizeFields {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f9f9fb;
    border: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

#customSizeFields input {
    width: 140px;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    font-size: 0.9rem;
}

@media (max-width: 576px) {
    #customSizeFields {
        flex-direction: column;
    }

    #customSizeFields input {
        width: 100%;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

.content-area,
.row.g-4.align-items-stretch {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
}

.col-lg-7,
.col-lg-5 {
    flex: 1;
    max-width: 50%;
}

.id-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    min-height: 300px;
    box-sizing: border-box;
    margin: 0 auto;
    overflow: hidden;
}

.id-card.loaded {
    border-color: var(--success-color);
    box-shadow: 0 10px 30px rgba(5, 150, 105, 0.2);
}

.id-card-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.id-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.employee-info {
    margin-top: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-label {
    font-weight: 600;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-value {
    font-weight: 500;
    color: #1e293b;
    text-align: right;
}

.scanner-controls {
    text-align: center;
    margin-bottom: 2rem;
}

.scan-mode-toggle {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.mode-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mode-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.mode-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

#reader,
.scanner-section .border.bg-white.shadow-sm,
.border.rounded.p-3.bg-white.shadow-sm {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
    border: 2px solid rgba(102, 126, 234, 0.3) !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

#reader:hover,
.scanner-section .border.bg-white.shadow-sm:hover,
.border.rounded.p-3.bg-white.shadow-sm:hover {
    border-color: rgba(102, 126, 234, 0.6) !important;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

#usb-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    display: none;
}

#usb-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn-custom {
    padding: 0.75rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media print {
    #paperBoundary {
        background: none !important;
        padding: 0 !important;
    }
    body {
        background: white !important;
    }
    .main-container,
    .preview-section,
    .id-card {
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100%;
        height: 100%;
    }
    .id-card {
        page-break-before: always;
    }
    .scanner-section,
    .header,
    .action-buttons,
    .setting-btn,
    .status-indicator,
    .scanner-controls {
        display: none !important;
    }
    .preview-section {
        width: 100% !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 auto !important;
    }
    #id-preview {
        width: 100%;
        height: 100%;
    }
}

.btn-print {
    background: var(--success-color);
    color: white;
}

.btn-print:hover {
    background: #047857;
    transform: translateY(-2px);
}

.btn-reset {
    background: var(--warning-color);
    color: white;
}

.btn-reset:hover {
    background: #b45309;
    transform: translateY(-2px);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--danger-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading-spinner {
    display: none;
    margin: 1rem auto;
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 1rem;
    margin: 1rem 0;
    display: none;
}

@media (max-width: 991px) {
    .content-area,
    .row.g-4.align-items-stretch {
        flex-direction: column;
        flex-wrap: wrap;
    }
    .col-lg-7,
    .col-lg-5 {
        max-width: 100%;
    }
    #reader {
        max-width: 100% !important;
    }
}

#scaledPreviewWrapper {
    transform: scale(1);
    transform-origin: center center;
}

#idPreviewContainer {
    background-color: white;
    position: relative;
    z-index: 1;
    box-sizing: border-box;
    overflow: hidden;
    border: 1px solid #ddd;
    border-radius: 5px;
    transform: scale(1);
    transition: transform 0.3s ease-in-out;
}

#paperBoundary {
    width: 500px;
    height: 500px;
    overflow: hidden;
    border-radius: 10px;
    margin: auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    /* Light border to contain the blur */
    border: 1px solid rgba(255, 255, 255, 0.2);
}

#paperBoundary::before {
    content: "";
    position: absolute;
    inset: -15px; /* Extend blur beyond container */
    background-image: inherit;
    background-size: cover;
    background-position: center;
    /* Softer blur with maintained brightness */
    filter: blur(12px) brightness(1);
    z-index: 0;
    /* Very light overlay for smoothness */
    background-color: rgba(255, 255, 255, 0.15);
    background-blend-mode: soft-light;
    /* Performance optimization */
    will-change: transform;
}

.id-overlay div {
    font-weight: 600;
    font-size: 1rem;
    color: #111827;
    /* Subtle white shadow for better contrast */
    text-shadow: 
        0 1px 1px rgba(255, 255, 255, 0.7),
        0 0 8px rgba(255, 255, 255, 0.4);
}

/* Specific styling for the header text */
.id-overlay div:first-child {
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 0.05em;
}

/* Styling for the ID number */
.id-overlay div:nth-child(2) {
    font-size: 1.5rem;
    margin: 0.5rem 0;
}

h5.mb-3 {
    background: #dfd4bd;
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    display: inline-block;
    font-weight: 600;
}
