<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>First Run Setup - ID QR Printing System - jLagzn STUDIO</title>
    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
    />
    <link
      rel="apple-touch-icon"
      href="{{ url_for('static', filename='icons/icon-192.png') }}"
    />
    <meta
      name="description"
      content="Initial setup for QR-based employee ID system by jLagzn STUDIO"
    />
    <meta name="author" content="jLagzn STUDIO" />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/index.css') }}"
    />
  </head>
  <body class="bg-light">
    <!-- Loading overlay with spinner -->
    <div class="loading-overlay" id="loadingOverlay">
      <div class="spinner">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>

    <div class="setup-container">
      <div class="header-logo">
        <img
          src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
          alt="Logo"
        />
        <h1 class="text-center mb-4">ID QR Printing System</h1>
      </div>
      <p class="text-center mb-4 text-muted">
        Let's get started with the initial setup
      </p>
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %} {% for category, message in messages %}
      <div
        class="alert alert-{{ 'danger' if category == 'error' else 'success' }} mb-4"
      >
        {{ message }}
      </div>
      {% endfor %} {% endif %} {% endwith %}

      <form
        id="setupForm"
        action="{{ url_for('activate') }}"
        method="POST"
        enctype="multipart/form-data"
      >
        <!-- Step 1: Upload Dataset -->
        <div class="step">
          <h3><span class="step-number">1</span> Upload Participant Dataset</h3>
          <p class="text-muted">
            Upload a CSV file containing participant data with columns: ID,
            Name, Position, Company
          </p>

          <div
            class="upload-area"
            onclick="document.getElementById('datasetInput').click()"
          >
            <i class="fas fa-upload fa-3x mb-3" style="color: #0d6efd"></i>
            <p>Click to upload dataset CSV file</p>
            <input
              type="file"
              name="dataset_file"
              id="datasetInput"
              accept=".csv"
              class="d-none"
              onchange="previewDatasetFile(this)"
              required
            />
          </div>

          <div id="datasetPreview" class="d-none">
            <div class="alert alert-info">
              <strong>Selected file:</strong> <span id="datasetFilename"></span>
            </div>
          </div>

          {% if dataset_files %}
          <div class="mt-3">
            <h5>Existing Datasets:</h5>
            <div class="file-list">
              <div class="list-group">
                {% for file in dataset_files %}
                <div class="list-group-item">
                  <input
                    class="form-check-input me-2"
                    type="radio"
                    name="existing_dataset"
                    id="dataset_{{ loop.index }}"
                    value="{{ file }}"
                  />
                  <label
                    class="form-check-label"
                    for="dataset_{{ loop.index }}"
                  >
                    {{ file }}
                  </label>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Step 2: Upload Template -->
        <div class="step">
          <h3><span class="step-number">2</span> Upload ID Template</h3>
          <p class="text-muted">
            Upload a PNG/JPG image to use as the ID card template (min
            500x500px)
          </p>

          <div
            class="upload-area"
            onclick="document.getElementById('templateInput').click()"
          >
            <i class="fas fa-upload fa-3x mb-3" style="color: #0d6efd"></i>
            <p>Click to upload template image</p>
            <input
              type="file"
              name="template_file"
              id="templateInput"
              accept=".png,.jpg,.jpeg"
              class="d-none"
              onchange="previewTemplateFile(this)"
              required
            />
          </div>

          <div id="templatePreview" class="d-none">
            <div class="alert alert-info">
              <strong>Selected file:</strong>
              <span id="templateFilename"></span>
            </div>
            <img
              id="templateThumbnail"
              src="#"
              alt="Template preview"
              class="img-thumbnail d-none"
              style="max-width: 200px"
            />
          </div>

          {% if template_files %}
          <div class="mt-3">
            <h5>Existing Templates:</h5>
            <div class="file-list">
              <div class="list-group">
                {% for file in template_files %}
                <div class="list-group-item">
                  <input
                    class="form-check-input me-2"
                    type="radio"
                    name="existing_template"
                    id="template_{{ loop.index }}"
                    value="{{ file }}"
                  />
                  <label
                    class="form-check-label"
                    for="template_{{ loop.index }}"
                  >
                    {{ file }}
                  </label>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Step 3: Complete Setup -->
        <div class="text-center mt-4">
          <input type="hidden" name="triggeredBy" value="first_run" />
          <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
            <i class="fas fa-check-circle me-2"></i> Complete Setup
          </button>
        </div>
      </form>

      <div class="footer">
        <img
          src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
          alt="Logo"
        />
        ID System v1.0 © 2025 jLagzn STUDIO | All rights reserved
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
      function previewDatasetFile(input) {
        if (input.files && input.files[0]) {
          document.getElementById("datasetFilename").textContent =
            input.files[0].name;
          document.getElementById("datasetPreview").classList.remove("d-none");

          // Uncheck any existing dataset radios
          document
            .querySelectorAll('input[name="existing_dataset"]')
            .forEach((radio) => {
              radio.checked = false;
            });
        }
      }

      function previewTemplateFile(input) {
        if (input.files && input.files[0]) {
          document.getElementById("templateFilename").textContent =
            input.files[0].name;
          document.getElementById("templatePreview").classList.remove("d-none");

          // Preview image
          const reader = new FileReader();
          reader.onload = function (e) {
            const img = document.getElementById("templateThumbnail");
            img.src = e.target.result;
            img.classList.remove("d-none");
          };
          reader.readAsDataURL(input.files[0]);

          // Uncheck any existing template radios
          document
            .querySelectorAll('input[name="existing_template"]')
            .forEach((radio) => {
              radio.checked = false;
            });
        }
      }

      // Form validation and loading spinner
      document
        .getElementById("setupForm")
        .addEventListener("submit", function (e) {
          const datasetSelected =
            document.querySelector('input[name="existing_dataset"]:checked') ||
            document.getElementById("datasetInput").files.length > 0;
          const templateSelected =
            document.querySelector('input[name="existing_template"]:checked') ||
            document.getElementById("templateInput").files.length > 0;

          if (!datasetSelected || !templateSelected) {
            e.preventDefault();
            alert(
              "Please select or upload both a dataset and a template before proceeding."
            );
          } else {
            // Show loading spinner
            document.getElementById("loadingOverlay").style.display = "flex";
            document.getElementById("submitBtn").disabled = true;

            // Clear file inputs if existing files are selected
            const existingDataset = document.querySelector(
              'input[name="existing_dataset"]:checked'
            );
            const existingTemplate = document.querySelector(
              'input[name="existing_template"]:checked'
            );

            if (existingDataset) {
              document
                .getElementById("datasetInput")
                .removeAttribute("required");
            }
            if (existingTemplate) {
              document
                .getElementById("templateInput")
                .removeAttribute("required");
            }

            // The form will submit normally and the page will refresh when done
            // If you're using AJAX, you would hide the spinner in the success/error callbacks
          }
        });

      // When selecting existing files, clear the file inputs
      document
        .querySelectorAll('input[type="radio"][name^="existing"]')
        .forEach((radio) => {
          radio.addEventListener("change", function () {
            if (this.name === "existing_dataset") {
              document.getElementById("datasetInput").value = "";
              document.getElementById("datasetPreview").classList.add("d-none");
            } else {
              document.getElementById("templateInput").value = "";
              document
                .getElementById("templatePreview")
                .classList.add("d-none");
            }
          });
        });

      // Hide loading spinner when page finishes loading (in case of form submission)
      window.addEventListener("load", function () {
        document.getElementById("loadingOverlay").style.display = "none";
        document.getElementById("submitBtn").disabled = false;
      });
      // Add file size validation
      function validateFile(input, maxSizeMB, allowedTypes) {
        if (input.files.length > 0) {
          const file = input.files[0];
          const fileType = file.type;
          const fileSizeMB = file.size / (1024 * 1024);

          if (fileSizeMB > maxSizeMB) {
            alert(`File size exceeds ${maxSizeMB}MB limit`);
            input.value = "";
            return false;
          }

          if (allowedTypes && !allowedTypes.includes(fileType)) {
            alert("Invalid file type. Please upload a supported file.");
            input.value = "";
            return false;
          }

          return true;
        }
        return false;
      }

      // Update the file input handlers
      document
        .getElementById("datasetInput")
        .addEventListener("change", function (e) {
          if (
            validateFile(this, 10, ["text/csv", "application/vnd.ms-excel"])
          ) {
            previewDatasetFile(this);
          }
        });

      document
        .getElementById("templateInput")
        .addEventListener("change", function (e) {
          if (
            validateFile(this, 5, ["image/jpeg", "image/png", "image/webp"])
          ) {
            previewTemplateFile(this);
          }
        });

      // Add loading state for form submission
      document
        .getElementById("setupForm")
        .addEventListener("submit", function (e) {
          const submitBtn = document.getElementById("submitBtn");
          submitBtn.innerHTML =
            '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
          submitBtn.disabled = true;
        });

      function validateFileSize(input, maxSizeMB) {
        if (input.files && input.files[0]) {
          const fileSizeMB = input.files[0].size / (1024 * 1024);
          if (fileSizeMB > maxSizeMB) {
            alert(
              `File size (${fileSizeMB.toFixed(
                2
              )}MB) exceeds maximum allowed size of ${maxSizeMB}MB`
            );
            input.value = "";
            return false;
          }
        }
        return true;
      }

      document
        .getElementById("datasetInput")
        .addEventListener("change", function (e) {
          if (validateFileSize(this, 10)) {
            // 10MB max
            previewDatasetFile(this);
          }
        });

      document
        .getElementById("templateInput")
        .addEventListener("change", function (e) {
          if (validateFileSize(this, 5)) {
            // 5MB max
            previewTemplateFile(this);
          }
        });
    </script>
  </body>
</html>
