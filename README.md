# ID QR Printing System with Email Integration

A comprehensive QR code-based employee ID system with email functionality, enhanced visual styling, and robust data management.

## 🚀 New Features

### 📧 Email Integration
- **Automatic Email Detection**: Automatically detects if your CSV file contains email addresses
- **QR Code Email Delivery**: Send QR codes directly to employee email addresses
- **HTML Email Templates**: Professional, responsive email templates with employee information
- **SMTP Configuration**: Easy setup for Gmail, Outlook, or custom SMTP servers
- **Email Validation**: Built-in email address validation and error handling

### 🎨 Enhanced Visual Design
- **Modern UI**: Completely redesigned interface with gradient backgrounds and animations
- **Glass Morphism**: Beautiful backdrop blur effects and translucent elements
- **Responsive Design**: Optimized for all screen sizes and devices
- **Interactive Elements**: Hover effects, smooth transitions, and loading animations
- **Professional Styling**: Corporate-ready design with consistent branding

### 📊 Smart Data Management
- **Header Normalization**: Automatically handles different column name formats (ID/id/Id, EMAIL/email/Email, etc.)
- **Data Cleanup**: Automatically removes old QR codes and preview files when uploading new datasets
- **Email Column Support**: Seamlessly handles CSVs with or without email columns
- **First Run Setup**: Guided setup wizard for initial configuration

## 📋 Requirements

```
Flask==3.0.0
pandas==2.1.3
qrcode==7.4.2
Pillow==10.0.1
cryptography==41.0.5
reportlab==4.0.4
pywebview==4.2
Werkzeug==3.0.0
gunicorn==21.2.0
python-dotenv==1.0.0
html5-qrcode
email-validator==2.1.0
```

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ID_QR_PRINTING
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure email settings** (optional)
   ```bash
   cp .env.example .env
   # Edit .env with your email configuration
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

## 📧 Email Configuration

### Gmail Setup
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
3. Use these settings:
   - SMTP Server: `smtp.gmail.com`
   - Port: `587`
   - Use TLS: `True`
   - Email: Your Gmail address
   - Password: The generated app password

### Other Email Providers
- **Outlook**: `smtp-mail.outlook.com:587`
- **Yahoo**: `smtp.mail.yahoo.com:587`
- **Custom SMTP**: Configure your own server settings

## 📊 CSV File Format

### Required Columns
- `ID`: Employee ID (numeric)
- `Name`: Employee full name
- `Position`: Job title/position
- `Company`: Company name

### Optional Columns
- `Email`: Employee email address (enables email functionality)

### Example CSV with Email Support
```csv
ID,Name,Position,Company,Email
1,John Doe,Software Engineer,TechCorp,<EMAIL>
2,Jane Smith,Project Manager,TechCorp,<EMAIL>
3,Bob Johnson,Designer,DesignCo,<EMAIL>
```

### Header Flexibility
The system automatically normalizes column headers, so these variations work:
- `id`, `Id`, `ID`
- `name`, `Name`, `NAME`
- `email`, `Email`, `EMAIL`, `e-mail`, `E-Mail`, `mail`
- `position`, `Position`, `POSITION`
- `company`, `Company`, `COMPANY`

## 🎯 Usage

### First Run Setup
1. Launch the application
2. Upload your employee CSV file
3. Upload an ID template image
4. Configure paper size and email settings
5. Complete setup

### Daily Operations
1. **Scan QR Codes**: Use camera or USB scanner
2. **View Employee Info**: Automatically displays after scanning
3. **Print ID Cards**: Automatic printing with preview option
4. **Send via Email**: Click "Send QR" to email QR codes to employees

### Email Features
- **Auto-populate**: Email addresses from CSV are automatically filled
- **Manual Entry**: Enter any email address for QR delivery
- **Validation**: Built-in email format validation
- **Status Feedback**: Real-time sending status and error messages

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test email functionality
python test_email_functionality.py

# Test enhanced features
python test_enhanced_features.py

# Demo email features
python demo_email_features.py
```

## 🔧 Configuration Options

### Paper Sizes
- A4, A5, A6, A7
- Letter, Legal
- B4, B5
- 4x6, 5x7, 5x8, 9x13
- Custom dimensions

### Email Settings
- SMTP server and port
- TLS/SSL encryption
- Authentication credentials
- Sender name customization

## 📁 File Structure

```
ID_QR_PRINTING/
├── app.py                          # Main Flask application
├── email_service.py                # Email functionality
├── requirements.txt                # Python dependencies
├── .env.example                    # Environment variables template
├── templates/
│   ├── index.html                  # Main interface
│   ├── first_run.html             # Setup wizard
│   ├── preview.html               # Print preview
│   └── print.html                 # Print layout
├── static/
│   ├── css/
│   │   ├── index.css              # Main styles
│   │   └── button.css             # Button animations
│   ├── qr_codes/                  # Generated QR codes
│   └── id_templates/              # ID card templates
├── participant_list/               # CSV datasets
├── test_email_functionality.py    # Email tests
├── test_enhanced_features.py      # Feature tests
└── demo_email_features.py         # Demo script
```

## 🚨 Troubleshooting

### Email Issues
- **Authentication Failed**: Check email credentials and app password
- **Connection Timeout**: Verify SMTP server and port settings
- **TLS Errors**: Ensure TLS is enabled for secure connections

### CSV Issues
- **Missing Columns**: Ensure required columns (ID, Name, Position, Company) exist
- **Header Problems**: Use the header normalization feature
- **Encoding Issues**: Save CSV files in UTF-8 encoding

### QR Code Issues
- **Generation Failed**: Check employee data completeness
- **Scanning Problems**: Ensure good lighting and camera focus
- **Print Quality**: Verify template resolution and printer settings

## 🔒 Security

- **Data Encryption**: QR codes contain encrypted employee data
- **Secure Email**: TLS encryption for email transmission
- **Local Storage**: All data stored locally, no cloud dependencies
- **Access Control**: Application runs locally with no external access

## 📞 Support

For issues, feature requests, or questions:
1. Check the troubleshooting section
2. Run the test scripts to verify functionality
3. Review the demo script for usage examples

## 📄 License

© 2025 jLagzn STUDIO. All rights reserved.

---

**Version**: 2.0.0 with Email Integration
**Last Updated**: January 2025
